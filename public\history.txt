Universal Live Tracking and Recording App(ULTRA) 更新履歴
2025/08/03 Version 1.10.1
1. Windows11環境にて、キーボード操作でタスクトレーからULTRAを起動できない問題を修正しました。
2. 通知音を鳴らすように設定している場合に、自動録画が動作しない場合があった不具合を修正しました。
3. ツイキャス録画時の画質が下がってしまった問題に対応するため、可能な場合は高画質版のURLを使用するように修正しました。
4. ツイキャスの通知対象ユーザーを保存しているファイルが壊れてしまい、アプリが正しく動作しなくなる問題を改善すべく、対処を試みました。

2025/05/18 Version 1.10.0
1. ツイキャスの「ログイン状態で録画」が再び動作しなくなった問題を修正しました。
2. 上記1.への対応のため、同梱のffmpegを更新しました。これに伴い、32ビット環境では動作しなくなります。
3. 開発環境をPython 3.11に更新しました。
4. yt-dlpを2025.4.30に更新しました。
5. ウィンドウを表示している状態でホットキーによるウィンドウ表示を行った際に、ウィンドウを最前面に表示するように修正しました。
6. 「/」など、特定の文字を含むライブを正しく処理できていなかった問題を修正しました。

2025/03/20 Version 1.9.5
1. ツイキャスの「ログイン状態で録画」でエラーが発生するようになった問題を修正しました。

2025/02/22 Version 1.9.4
1. ツイキャスとの接続に成功しなかったり、新着ライブの通知が動作しなかったりする問題に対応しました。詳しくは、readme.txtの「４．１．5　ULTRA Version 1.9.4への更新に伴う新着ライブの取得方法の変更について」をご参照ください。

2024/11/17 Version 1.9.3
1. 録画中にアプリを終了すると、再生できないファイルが保存されてしまう問題を修正しました。
2. yt-dlpを2024.10.22に更新しました。

2024/08/12 Version 1.9.2
1. ツイキャスの「指定したユーザのライブを録画」機能を使用した際、「ログイン状態で録画」の設定が反映されない問題、画質が下がってしまう問題を修正しました。
2. yt-dlpを2024.8.6に更新しました。

2024/05/19 Version 1.9.1
1. yt-dlpでプレイリストを一括ダウンロードする際、利用できない動画が含まれていると正しく動作しない問題を修正しました。

2024/03/20 Version 1.9.0
1. 録画ファイル名に、それぞれの動画を識別する文字列を付加できるようになりました。ファイル名の設定で「%movie%」と入力した部分が、各動画の識別子に置換されます。
2. ツイキャスの録画において、実際の配信よりも画質が下がってしまう問題を修正しました。
3. yt-dlpの一括ダウンロードについて、インターネットに接続されていない場合などにアプリが異常終了する問題を修正しました。
4. ツイキャスの一括追加で「c:」などで始まるアカウントを追加できない問題を修正しました。
5. yt-dlpを2024.3.10に更新しました。

2023/08/14 Version 1.8.1
1. yt-dlpでの一括ダウンロードに関して、一括ダウンロードURLを1件も登録していない状態で本ソフトを起動すると、無限ループとなり、コンピュータのリソースを大量に消費する問題を修正しました。

2023/08/11 Version 1.8.0
1. yt-dlpを使用して、プレイリストのダウンロードができるようになりました。
2. yt-dlpでダウンロードするプレイリストのURLを登録し、定期的にダウンロードを実行できる機能を追加しました。

2023/07/17 Version 1.7.0
1. 「yt-dlp」との連携機能を追加し、YouTubeなどの動画をダウンロードできるようになりました。現状、最新の動画を定期的に取得することはできませんが、再生ページのURLを入力することで、お好きな動画をダウンロードしていただけます。

2023/04/29 Version 1.6.0
1. Twitterスペースとの連携機能を廃止しました。Twitter APIの変更により、この機能の継続的な運用が難しくなりました。これまでご愛顧いただき、ありがとうございました。
2. ツイキャス連携機能において、[Twitterでフォローしているユーザを一括追加]を廃止しました。
3. 上記2.の代替手段として、[ユーザを一括追加]を追加しました。複数のユーザ名を改行区切りで入力し、まとめて追加できる機能です。

2023/02/11 Version 1.5.2
1. [ログイン状態で録画]および[過去ライブのダウンロード]でTwitterアカウントにログインする際、ツイキャスのページで設定された「ログインパスワード」を使用するように変更しました。この機能を使用する際は、事前にツイキャスのサイトでログインパスワードを設定し、それをULTRAに入力する必要があります。詳細については、readme.txtの「４．１．４　利用に当たっての注意事項」をご参照ください。

2023/01/09 Version 1.5.1
1. Version 1.5.0において、ツイキャスへのログイン時のパスワード入力をキャンセルすると、ツイキャスの録画機能が正しく動作しなくなる問題を修正しました。

2023/01/09 Version 1.5.0
1. ツイキャス連携機能において、アカウントにログインした状態での録画に対応しました。

2022/11/23 Version 1.4.0
1. 録画形式の設定を、サービス毎に独立して行うように変更しました。
2. ツイキャス連携機能について、
・過去ライブのダウンロード機能において、プレミア配信など、特定のアカウントでしか閲覧できない録画のダウンロードに対応しました。ただし、ULTRAと連携しているアカウントで閲覧可能なものに限ります。
・過去ライブのダウンロードができなくなった問題を修正しました。
・過去ライブをMP4形式でダウンロードできない問題を修正しました。
3. 上記1.に伴い、Twitterスペースの録画形式として、TS形式とMP4形式を選択できないように変更しました。現時点でサポートしているのはMP3形式のみです。
4. プロキシサーバーを使用する場合の挙動を改善しました。なお、プロキシ環境での利用については、readme.txtの注意事項を参照してください。
5. 録画に失敗したことが画面に表示されない場合があったのを修正しました。

2022/02/20 Version 1.3.1
1. ツイキャス連携機能において、特定の場合に、プロキシサーバを通した接続ができない問題を修正しました。
2. Twitterスペースとの連携機能について、
・頻繁に認証エラーが発生する問題を修正しました。
・ULTRAの起動から一定時間経過後にスペースの開始を検知した際、アプリが異常終了する問題を修正しました。
3. 最新バージョンの確認を複数回実行すると、アプリが異常終了する問題に対応しました。
4. Twitter スペースとツイキャスの監視を同時に行っているとき、ツイキャスとの接続が頻繁に切断されてしまう問題を修正しました。

2022/02/11 Version 1.3.0
1. Twitter スペースとの連携機能を追加しました。
2. ツイキャス連携機能が無効になっているとき、[Twitterでフォローしているユーザを一括追加]を実行できないように修正しました。
3. キーボードショートカットおよびグローバルホットキーの設定において、各サービスに固有の機能には、サービス名を前置して標示するようになりました。
4. グローバルホットキーの[ウィンドウを表示]を実行したとき、ULTRAのウィンドウ上で最後にフォーカスがあった項目に自動でフォーカスを移動するようにしました。
5. バルーン通知において、通知の送信元のサービス名を標示するように変更しました。

2022/02/02 Version 1.2.1
1. ツイキャス連携機能が無効になっているとき、[過去ライブの一括ダウンロード]を実行できないように修正しました。
2. [Twitterでフォローしているユーザを一括追加]が正しく動作しなくなった問題を修正しました。

2022/01/08 Version 1.2.0
1. ツイキャス連携機能について、
・指定したユーザの過去ライブを全て録画する機能を追加しました。
・特定の場合に、過去ライブのダウンロードに失敗するようになった問題を修正しました。
2. コンピュータをシャットダウンするなどして本ソフトを強制終了させた場合に、オプション設定が保存されなかったのを修正しました。
3. ウィンドウが表示されている状態で、グローバルショートカットの「ウィンドウを表示」を実行した場合、ULTRAがフォーカスされるように修正しました。

2021/05/03 Version 1.1.0
1. ツイキャス連携機能について
・「指定したユーザのライブを録画」に関する各種不具合を修正しました。
・「過去ライブのダウンロード」に失敗するようになった問題を修正しました。
・Twitterアカウントのフォロー一覧を取得し、ツイキャスに登録されているアカウントを一括で通知対象ユーザに追加する機能を追加しました。
・[ユーザ情報を更新]の実行中に[通知対象ユーザの管理]を実行すると、意図しない情報が保存されてしまう可能性があったため、これらの機能を同時に使用できないように修正しました。
2. 特定の場合に、録画に失敗し、プログラムが不正終了する問題に対処しました。
3. 録画中にインターネット接続が切断された場合など、録画エラーが起きた際の挙動を改善しました。

2021/03/24 Version 1.0.2
1. ソフトウェアの更新機能が正しく動作しなかったのを修正しました。
2. 本ソフトのインストールフォルダ内に「license.txt」が存在しなかったのを修正しました。

2021/03/23 Version 1.0.1
1. ツイキャス連携機能について
・一定時間経過後にツイキャスとの接続が切れてしまう問題に対応しました。
・ツイキャスの配信を自動で録画した際、「録画が公開されていることを確認してください」と表示される場合があったのを修正しました。
・極端に短い配信が行われた場合に、録画に失敗し、ライブ終了後も「録画エラー」と表示され続ける問題を修正しました。
2. 特定の環境で、プログラムが起動できない問題を修正しました。
