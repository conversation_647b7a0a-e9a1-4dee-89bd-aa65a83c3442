version: development.{build}-{branch}
branches:
  only:
    - master

skip_branch_with_pr: true
skip_tags: true

environment:
  githubToken:
      secure: iNSx8v6c+0TP9IyckinrvNWuX+hc7SKruOqRDl9GxdX59sZ2yqB8+UDAtqdPzKIg
  SCRIPT_PASSWORD:
    secure: 9RvVUKVQ8MsoG5LOZ86VyFQF19JXXS33tDzomLH/jZM=
  matrix:

    # For Python versions available on Appveyor, see
    # http://www.appveyor.com/docs/installed-software#python
    # The list here is complete (excluding Python 2.6, which
    # isn't covered by this document) at the time of writing.

    - PYTHON: "C:\\Python311"

install:
  # We need wheel installed to build wheels
  - "%PYTHON%\\python.exe -m pip install -r requirements.txt"

build: off

test_script:
  # Put your test command here.
  # If you don't need to build C extensions on 64-bit Python 3.3 or 3.4,
  # you can remove "build.cmd" from the front of the command, as it's
  # only needed to support those cases.
  # Note that you must use the environment variable %PYTHON% to refer to
  # the interpreter you're using - A<PERSON>veyor does not do anything special
  # to put the Python evrsion you want to use on PATH.
  - "echo Skipped Tests"

after_test:
  # This step builds your wheels.
  # Again, you only need build.cmd if you're building C extensions for
  # 64-bit Python 3.3/3.4. And you need to use %PYTHON% to get the correct
  # interpreter
  - "%PYTHON%\\python.exe tools\\build.py --appveyor"
  - cmd: if defined APPVEYOR_PULL_REQUEST_NUMBER appveyor exit 0
  - cmd: curl "https://actlab.org/git-release.php?repo_name=%APPVEYOR_REPO_NAME%&tag_name=ULTRA-latestcommit&password=%SCRIPT_PASSWORD%"
  - cmd: git tag -d ULTRA-latestcommit
  - cmd: git push -d https://actlab-auto:%<EMAIL>/actlaboratory/ULTRA.git ULTRA-latestcommit
  - cmd: git tag ULTRA-latestcommit
  - cmd: git push https://actlab-auto:%<EMAIL>/actlaboratory/ULTRA.git ULTRA-latestcommit



artifacts:
  - path: ULTRA-*.zip
  - path: ULTRA-*.json

deploy:
  - provider: GitHub
    release: ULTRA-latestcommit
    skip_tags: true
    Force update: true
    description: 'automatic build from master branch'
    auth_token:
      secure: iNSx8v6c+0TP9IyckinrvNWuX+hc7SKruOqRDl9GxdX59sZ2yqB8+UDAtqdPzKIg
    artifact: /(ULTRA-.*\.zip)|(ULTRA-.*\.json)/                # upload all NuGet packages to release assets
    draft: false
    prerelease: false
    on:
      branch: master                 # release from master branch only


after_deploy:
  - cmd: echo import constants;print("https://actlab.org/api/addAlphaVersion?repo_name=%APPVEYOR_REPO_NAME%&commit_hash=%APPVEYOR_REPO_COMMIT%&version="+constants.APP_VERSION+"&password=%SCRIPT_PASSWORD%",end="") | C:\\Python311\python.exe | xargs -n1 curl
