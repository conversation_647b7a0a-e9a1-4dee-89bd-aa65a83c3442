Copyright (c)2021 <PERSON><PERSON><PERSON>,ACT laboratory All rights reserved.

本ソフトウェアは、Apache License 2.0の条件に従い、自由に利用することができます。
本ソフトウェアのソースコードは、readme.txtに記載のURLよりダウンロード可能です。

※本ソフトウェアには、以下の各ライセンスに従い他者の成果物を含みます。

・Apacheライセンスv2に基づく以下のライブラリ
	requests　(https://github.com/psf/requests)
	pypac	(https://pypi.org/project/pypac/)
	libloader　(https://github.com/bmarwell/libloader/)

・BSDライセンスに基づく以下のライブラリ
	pyperclip	(https://pypi.org/project/pyperclip/)
		Copyright (c) 2014, Al Sweigart
	pybass　(https://pypi.org/project/pybass/)
		Copyright(c) Maxim Kolosov 2009-2013 <EMAIL>
	websocket-client
		Copyright 2018 Hiroki Ohtani.
　　tweepy　(https://www.tweepy.org/)
　　　　Copyright (c) 2009-2021 <PERSON>lein

・MITライセンスに基づく以下のライブラリ
	AccessibleOutput2　(https://pypi.org/project/accessible-output2/)

・MS-PLに基づく以下のライブラリ
	DotNetZip

・GNU GENERAL PUBLIC LICENSE(GPL) v3に基づく以下のライブラリ
	ffmpeg




その他、個別のライセンスに基づく複数のライブラリ

各ライセンスは、licensesディレクトリ内に格納されています。
