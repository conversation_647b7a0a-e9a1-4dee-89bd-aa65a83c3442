# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the ULTRA package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: ULTRA\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-31 21:57+0900\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: app.py:66 menuItemsDic.py:31 menuItemsDic.py:36
msgid "録画形式の設定"
msgstr ""

#: app.py:66
#, python-format
msgid ""
"%(source)sの録画形式として%(ext)s形式が使用できなくなりました。規定値"
"の%(ext_default)s形式に変更します。"
msgstr ""

#: AppBase.py:51
msgid ""
"ログ機能の初期化に失敗しました。下記のファイルへのアクセスが可能であることを"
"確認してください。"
msgstr ""

#: AppBase.py:60
msgid "音声エンジンエラー"
msgstr ""

#: AppBase.py:60
msgid ""
"音声読み上げ機能の初期化に失敗したため、読み上げ機能を使用できません。出力先"
"の変更をお試しください。"
msgstr ""

#: keymapHandlerBase.py:696 keymapHandlerBase.py:726
#, python-format
msgid "%s は存在しないキーです。"
msgstr ""

#: keymapHandlerBase.py:767
#, python-format
msgid "%s は使用できないキーです。"
msgstr ""

#: keymapHandlerBase.py:776
msgid "修飾キーのみのパターンは設定できません。"
msgstr ""

#: keymapHandlerBase.py:778
msgid "このキーは修飾キーと合わせて指定する必要があります。"
msgstr ""

#: keymapHandlerBase.py:783
msgid "修飾キーでないキーを複数指定することはできません。"
msgstr ""

#: keymapHandlerBase.py:786 keymapHandlerBase.py:794
msgid "このキーは、SHIFTキー以外の修飾キーと合わせて指定する必要があります。"
msgstr ""

#: keymapHandlerBase.py:789
msgid "修飾キーのみの組み合わせは指定できません。"
msgstr ""

#: keymapHandlerBase.py:798
msgid "この組み合わせは別の用途で予約されているため、利用できません。"
msgstr ""

#: menuItemsDic.py:15
msgid "ウィンドウを表示(&S)"
msgstr ""

#: menuItemsDic.py:16
msgid "ウィンドウを隠す(&H)"
msgstr ""

#: menuItemsDic.py:17
msgid "終了(&X)"
msgstr ""

#: menuItemsDic.py:18
msgid "ツイキャス(&T)"
msgstr ""

#: menuItemsDic.py:19
msgid "ツイキャス連携機能を有効化(&E)"
msgstr ""

#: menuItemsDic.py:20
msgid "録画時にコメントをテキストファイルに保存する "
msgstr ""

#: menuItemsDic.py:21 sources\twitcasting.py:236
msgid "ログイン状態で録画"
msgstr ""

#: menuItemsDic.py:22
msgid "ユーザ情報を更新(&U)"
msgstr ""

#: menuItemsDic.py:23
msgid "ユーザを一括追加(&B)"
msgstr ""

#: menuItemsDic.py:24
msgid "過去ライブのダウンロード(&A)"
msgstr ""

#: menuItemsDic.py:25
msgid "過去ライブの一括ダウンロード(&D)"
msgstr ""

#: menuItemsDic.py:26
msgid "指定したユーザのライブを録画(&R)"
msgstr ""

#: menuItemsDic.py:27
msgid "ログインセッションを削除"
msgstr ""

#: menuItemsDic.py:28
msgid "アクセストークンを削除"
msgstr ""

#: menuItemsDic.py:29
msgid "アクセストークンを設定(&T)"
msgstr ""

#: menuItemsDic.py:30
msgid "通知対象ユーザの管理(&M)"
msgstr ""

#: menuItemsDic.py:32
msgid "その他のサービス（&yt-dlp）"
msgstr ""

#: menuItemsDic.py:33
msgid "一括ダウンロードを有効化(&E)"
msgstr ""

#: menuItemsDic.py:34
msgid "&URLを指定してダウンロード"
msgstr ""

#: menuItemsDic.py:35
msgid "一括ダウンロードURLの管理(&M)"
msgstr ""

#: menuItemsDic.py:37
msgid "設定(&S)"
msgstr ""

#: menuItemsDic.py:38
msgid "キーボードショートカットの設定(&K)"
msgstr ""

#: menuItemsDic.py:39
msgid "グローバルホットキーの設定(&H)"
msgstr ""

#: menuItemsDic.py:40
msgid "Windows起動時の自動起動を有効化(&W)"
msgstr ""

#: menuItemsDic.py:41
msgid "最新バージョンを確認(&U)"
msgstr ""

#: menuItemsDic.py:42
msgid "バージョン情報(&V)"
msgstr ""

#: notificationHandler.py:52
#, python-format
msgid "配信開始：%s、サービス：%s"
msgstr ""

#: recorder.py:198 recorder.py:200 recorder.py:211 recorder.py:237
#: recorder.py:241 recorder.py:243
msgid "録画エラー"
msgstr ""

#: recorder.py:198
msgid ""
"録画の開始に失敗しました。録画の保存先が適切に設定されていることを確認してく"
"ださい。定期的に再試行する場合は[はい]、処理を中断する場合は[いいえ]を選択し"
"てください。[はい]を選択して録画の保存先を変更することで、正しく録画を開始で"
"きる場合があります。"
msgstr ""

#: recorder.py:200 recorder.py:211
#, python-format
msgid "%sのライブの録画処理を中断しました。"
msgstr ""

#: recorder.py:214
msgid "録画開始"
msgstr ""

#: recorder.py:214 recorder.py:245 sources\twitcasting.py:879
#: sources\twitcasting.py:891
#, python-format
msgid "ユーザ：%(user)s、ムービーID：%(movie)s"
msgstr ""

#: recorder.py:215
msgid "録画中"
msgstr ""

#: recorder.py:237 recorder.py:241
#, python-format
msgid "%sのライブを録画中にエラーが発生しました。"
msgstr ""

#: recorder.py:237 recorder.py:241 recorder.py:243
#, python-format
msgid "詳細：%s"
msgstr ""

#: recorder.py:243
#, python-format
msgid "%sのライブを録画中にエラーが発生したため、再度録画を開始します。"
msgstr ""

#: recorder.py:245
msgid "録画終了"
msgstr ""

#: update.py:50 update.py:59 update.py:73 update.py:79 update.py:90
#: update.py:98 update.py:103 update.py:108 update.py:159
#: views\updateDialog.py:39
msgid "アップデート"
msgstr ""

#: update.py:50
msgid "このバージョンではアップデートを使用できません。"
msgstr ""

#: update.py:59
msgid ""
"アップデータが利用できません。お手数ですが、再度ソフトウェアをダウンロードし"
"てください。"
msgstr ""

#: update.py:73
msgid "サーバーへの通信がタイムアウトしました。"
msgstr ""

#: update.py:79
msgid "サーバーへの接続に失敗しました。インターネット接続などをご確認ください"
msgstr ""

#: update.py:85
msgid "サーバーとの通信中に不明なエラーが発生しました。"
msgstr ""

#: update.py:90
msgid "サーバーとの通信に失敗しました。"
msgstr ""

#: update.py:98
msgid "現在のバージョンが最新です。アップデートの必要はありません。"
msgstr ""

#: update.py:103
msgid "リクエストパラメーターが不正です。開発者まで連絡してください"
msgstr ""

#: update.py:108
msgid "アップデーターが登録されていません。開発者に連絡してください。"
msgstr ""

#: update.py:159
msgid ""
"ダウンロードが完了しました。\n"
"ソフトウェア終了時に、自動でアップデートされます。"
msgstr ""

#: sources\twitcasting.py:42 sources\twitcasting.py:1159 views\main.py:421
msgid "ツイキャス"
msgstr ""

#: sources\twitcasting.py:45
msgid "動画（MP4）"
msgstr ""

#: sources\twitcasting.py:46
msgid "動画（TS）"
msgstr ""

#: sources\twitcasting.py:47
msgid "音声のみ（MP3）"
msgstr ""

#: sources\twitcasting.py:61 sources\twitcasting.py:532
msgid "未接続"
msgstr ""

#: sources\twitcasting.py:103
msgid "アクセストークンの有効期限が切れています"
msgstr ""

#: sources\twitcasting.py:103 sources\twitcasting.py:110
msgid ""
"本ソフトの使用を続けるには、アクセストークンを再度設定する必要があります。"
msgstr ""

#: sources\twitcasting.py:105
msgid ""
"アクセストークンの有効期限が切れています。本ソフトの使用を続けるには、アクセ"
"ストークンを再度設定する必要があります。"
msgstr ""

#: sources\twitcasting.py:110
msgid "アクセストークンの有効期限が近づいています"
msgstr ""

#: sources\twitcasting.py:112
msgid ""
"アクセストークンの有効期限が近づいています。本ソフトの使用を続けるには、アク"
"セストークンを再度設定する必要があります。"
msgstr ""

#: sources\twitcasting.py:130
msgid ""
"ログインセッションが不正なため、ログイン状態での録画機能を無効にしました。再"
"度この機能を使用するには、メニューの[ログイン状態で録画]を選択し、パスワード"
"を入力する必要があります。"
msgstr ""

#: sources\twitcasting.py:199
msgid "配信開始"
msgstr ""

#: sources\twitcasting.py:208
msgid "録画対象の削除"
msgstr ""

#: sources\twitcasting.py:208 sources\twitcasting.py:209
#, python-format
msgid "%sのライブを、録画対象から削除しました。"
msgstr ""

#: sources\twitcasting.py:231
msgid "録画警告"
msgstr ""

#: sources\twitcasting.py:231
msgid ""
"高画質版URLを取得できませんでした。録画データの画質が低下する可能性がありま"
"す。"
msgstr ""

#: sources\twitcasting.py:236
msgid ""
"ログイン状態で録画するためのURLを取得できませんでした。未ログイン状態と同じ"
"URLで続行します。"
msgstr ""

#: sources\twitcasting.py:261
msgid "ユーザ名変更"
msgstr ""

#: sources\twitcasting.py:261 sources\twitcasting.py:264
#, python-format
msgid "「%(old)s」→「%(new)s」"
msgstr ""

#: sources\twitcasting.py:264
msgid "名前変更"
msgstr ""

#: sources\twitcasting.py:279 sources\twitcasting.py:531
msgid "切断"
msgstr ""

#: sources\twitcasting.py:279
msgid "インターネット接続が切断されました。再試行します。"
msgstr ""

#: sources\twitcasting.py:280
msgid "接続試行中"
msgstr ""

#: sources\twitcasting.py:314
msgid ""
"インターネット接続に失敗しました。現在ツイキャスとの連携機能を使用できませ"
"ん。"
msgstr ""

#: sources\twitcasting.py:335
msgid ""
"認証に成功しました。このウィンドウを閉じて、アプリケーションに戻ってくださ"
"い。"
msgstr ""

#: sources\twitcasting.py:336
msgid "認証に失敗しました。もう一度お試しください。"
msgstr ""

#: sources\twitcasting.py:337
msgid ""
"しばらくしても画面が切り替わらない場合は、別のブラウザでお試しください。"
msgstr ""

#: sources\twitcasting.py:341
msgid "アカウントの追加"
msgstr ""

#: sources\twitcasting.py:350 sources\twitcasting.py:362
msgid "処理結果"
msgstr ""

#: sources\twitcasting.py:350
msgid "キャンセルされました。"
msgstr ""

#: sources\twitcasting.py:362
msgid "認証が完了しました。"
msgstr ""

#: sources\twitcasting.py:404
msgid "ユーザ情報の保存に失敗しました。"
msgstr ""

#: sources\twitcasting.py:412
msgid "接続完了"
msgstr ""

#: sources\twitcasting.py:412
msgid "新着ライブの監視を開始しました。"
msgstr ""

#: sources\twitcasting.py:414
msgid "接続済み"
msgstr ""

#: sources\twitcasting.py:446
msgid "指定したユーザが見つかりません。"
msgstr ""

#: sources\twitcasting.py:531
msgid "ツイキャスとの接続を切断しました。"
msgstr ""

#: sources\twitcasting.py:567
msgid "入力されたURLの形式が不正です。内容をご確認の上、再度お試しください。"
msgstr ""

#: sources\twitcasting.py:570
msgid "過去ライブのダウンロード"
msgstr ""

#: sources\twitcasting.py:570
msgid ""
"ライブ情報の取得に失敗しました。プレミア配信など、一部のユーザにしか閲覧でき"
"ないライブの場合、ULTRAと連携しているアカウントでログインすることで、ダウン"
"ロードに成功する可能性があります。今すぐログインしますか？"
msgstr ""

#: sources\twitcasting.py:631
msgid "合い言葉の入力"
msgstr ""

#: sources\twitcasting.py:631
msgid "合い言葉"
msgstr ""

#: sources\twitcasting.py:716
msgid "アクセストークンが見つかりません"
msgstr ""

#: sources\twitcasting.py:716
msgid ""
"利用可能なアクセストークンが見つかりません。ブラウザを起動し、認証作業を行い"
"ますか？"
msgstr ""

#: sources\twitcasting.py:726
msgid ""
"録画に失敗しました。録画が公開されていること、入力したURLに誤りがないことを確"
"認してください。合い言葉を入力した場合は、入力した合い言葉に誤りがないことを"
"確認してください。"
msgstr ""

#: sources\twitcasting.py:735
msgid ""
"ツイキャスAPIの実行回数が上限に達しました。しばらくたってから、再度実行してく"
"ださい。"
msgstr ""

#: sources\twitcasting.py:737
msgid ""
"ツイキャスAPIが500エラーを返しました。しばらく待ってから、再度接続してくださ"
"い。"
msgstr ""

#: sources\twitcasting.py:739
msgid "現在ツイキャスとの連携機能を使用できません。開発者に連絡してください。"
msgstr ""

#: sources\twitcasting.py:752
#, python-format
msgid "ツイキャスAPIとの通信中にエラーが発生しました。詳細：%s"
msgstr ""

#: sources\twitcasting.py:786
msgid "このユーザのライブはすでに録画中です。"
msgstr ""

#: sources\twitcasting.py:791
msgid "このユーザはすでに登録されています。"
msgstr ""

#: sources\twitcasting.py:805
msgid "ユーザ名を指定して録画"
msgstr ""

#: sources\twitcasting.py:805
#, python-format
msgid ""
"%sを、録画対象として追加しました。この登録は一定時間経過後に自動で削除されま"
"す。"
msgstr ""

#: sources\twitcasting.py:828
msgid "対象ユーザの指定"
msgstr ""

#: sources\twitcasting.py:829
msgid ""
"対象アカウントの@からはじまるアカウント名を、\n"
"改行区切りで入力してください。"
msgstr ""

#: sources\twitcasting.py:879
msgid "コメント保存開始"
msgstr ""

#: sources\twitcasting.py:891
msgid "コメント保存終了"
msgstr ""

#: sources\twitcasting.py:998 sources\twitcasting.py:1001
#: sources\twitcasting.py:1006
msgid "ユーザ情報の更新"
msgstr ""

#: sources\twitcasting.py:998
msgid "ユーザ情報の更新を開始します。"
msgstr ""

#: sources\twitcasting.py:1001
#, python-format
msgid "%sの情報を取得しています。"
msgstr ""

#: sources\twitcasting.py:1006
msgid "ユーザ情報の更新が終了しました。"
msgstr ""

#: sources\twitcasting.py:1022
msgid "一括追加"
msgstr ""

#: sources\twitcasting.py:1026
msgid "処理を開始します。"
msgstr ""

#: sources\twitcasting.py:1046
#, python-format
msgid "%sを追加しました。"
msgstr ""

#: sources\twitcasting.py:1053
msgid "処理が終了しました。"
msgstr ""

#: sources\twitcasting.py:1098 sources\twitcasting.py:1101
#: sources\twitcasting.py:1105 sources\twitcasting.py:1110
#: sources\twitcasting.py:1113
msgid "一括録画"
msgstr ""

#: sources\twitcasting.py:1098
msgid "ライブ一覧を取得しています。"
msgstr ""

#: sources\twitcasting.py:1101
#, python-format
msgid "処理を開始します。対象ライブ数：%i"
msgstr ""

#: sources\twitcasting.py:1105
#, python-format
msgid "処理中（%(index)i/%(total)i）"
msgstr ""

#: sources\twitcasting.py:1110
msgid "ファイルが既に存在するため、録画をスキップします。"
msgstr ""

#: sources\twitcasting.py:1113
#, python-format
msgid "完了。%i件録画しました。"
msgstr ""

#: sources\twitcasting.py:1156
msgid "Twitter"
msgstr ""

#: sources\twitcasting.py:1161
msgid ""
"ログインに対応しているのはTwitterとツイキャスのアカウントのみです。その他の"
"サービスでのログインはできません。"
msgstr ""

#: sources\twitcasting.py:1164
#, python-format
msgid "%(service)sアカウント「%(account)s」のパスワードを入力"
msgstr ""

#: sources\twitcasting.py:1165
msgid "パスワードの入力"
msgstr ""

#: sources\twitcasting.py:1175 sources\twitcasting.py:1179
msgid "ログイン中にエラーが発生しました。"
msgstr ""

#: sources\twitcasting.py:1176
msgid "パスワードが正しくありません。"
msgstr ""

#: sources\twitcasting.py:1177
msgid ""
"パスワードが正しくありません。なお、ULTRA Version 1.5.2の更新に伴い、ツイキャ"
"スの「ログインパスワード」を使用してログインするようになりました。ツイキャス"
"のサイトでログインパスワードを設定し、そのパスワードをULTRAに設定する必要があ"
"ります。"
msgstr ""

#: sources\twitcasting.py:1178
msgid ""
"reCAPTCHAによる認証が必要です。ブラウザからTwitterにログインし、認証を行って"
"ください。"
msgstr ""

#: sources\twitcasting.py:1180
msgid "認証が必要です。ブラウザで操作を完了してください。"
msgstr ""

#: sources\ydl.py:28
msgid "その他のサービス（yt-dlp）"
msgstr ""

#: sources\ydl.py:31
msgid "動画"
msgstr ""

#: sources\ydl.py:32
msgid "音声のみ"
msgstr ""

#: sources\ydl.py:42 sources\ydl.py:185
msgid "一括ダウンロード無効"
msgstr ""

#: sources\ydl.py:46
msgid "一括ダウンロード有効"
msgstr ""

#: sources\ydl.py:90 sources\ydl.py:102 sources\ydl.py:116
msgid "ダウンロードエラー"
msgstr ""

#: sources\ydl.py:90 sources\ydl.py:92
#, python-format
msgid ""
"動画情報の取得に失敗しました。\n"
"詳細：%s"
msgstr ""

#: sources\ydl.py:102 sources\ydl.py:104
msgid "このプレイリストは、一括ダウンロードURLとして登録されています。"
msgstr ""

#: sources\ydl.py:116 sources\ydl.py:118
#, python-format
msgid "%sのダウンロードは現在サポートされていません。"
msgstr ""

#: sources\ydl.py:159
#, python-format
msgid ""
"プレイリストの取得に失敗しました。\n"
"詳細：%s"
msgstr ""

#: sources\ydl.py:171 sources\ydl.py:175 sources\ydl.py:179 sources\ydl.py:358
msgid "プレイリストの保存"
msgstr ""

#: sources\ydl.py:171
#, python-format
msgid "処理開始：%s"
msgstr ""

#: sources\ydl.py:175
#, python-format
msgid "処理終了：%s"
msgstr ""

#: sources\ydl.py:179
#, python-format
msgid "処理中止：%s"
msgstr ""

#: sources\ydl.py:358
#, python-format
msgid "処理中（%(title)s）：%(cnt)d/%(total)d"
msgstr ""

#: views\auth.py:23
msgid "ブラウザでの操作を待っています..."
msgstr ""

#: views\auth.py:24 views\KeyValueSettingDialogBase.py:85
#: views\KeyValueSettingDialogBase.py:279 views\settingsDialog.py:92
#: views\SimpleInputDialog.py:56 views\updateDialog.py:45
msgid "キャンセル"
msgstr ""

#: views\base.py:69
msgid "現在メニューは操作できません"
msgstr ""

#: views\base.py:117
msgid ""
"で設定されたショートカットキーが正しくありません。キーの重複、存在しないキー"
"名の指定、使用できないキーパターンの指定などが考えられます。以下のキーの設定"
"内容をご確認ください。\n"
"\n"
msgstr ""

#: views\base.py:120 views\fontManager.py:25 views\fontManager.py:50
#: views\globalKeyConfig.py:61 views\KeyValueSettingDialogBase.py:301
#: views\KeyValueSettingDialogBase.py:352 views\main.py:90
#: views\updateDialog.py:66
msgid "エラー"
msgstr ""

#: views\fontManager.py:25
msgid ""
"設定されているフォント情報が不正です。デフォルトのフォントを適用します。"
msgstr ""

#: views\fontManager.py:50
msgid "原因不明のエラーにより、フォントの変更に失敗しました。"
msgstr ""

#: views\globalKeyConfig.py:20 views\globalKeyConfig.py:74
#: views\tcManageUser.py:21 views\tcManageUser.py:100
msgid "名前"
msgstr ""

#: views\globalKeyConfig.py:21
msgid "ショートカット"
msgstr ""

#: views\globalKeyConfig.py:22 views\globalKeyConfig.py:74
msgid "識別子"
msgstr ""

#: views\globalKeyConfig.py:31 views\main.py:324
msgid "ショートカットキーの設定"
msgstr ""

#: views\globalKeyConfig.py:54 views\globalKeyConfig.py:71
#: views\globalKeyConfig.py:102 views\globalKeyConfig.py:107
#: views\globalKeyConfig.py:120 views\globalKeyConfig.py:128
#: views\globalKeyConfig.py:138 views\globalKeyConfig.py:143
#: views\keyConfig.py:57 views\KeyValueSettingDialogBase.py:337
#: views\main.py:429 views\main.py:448
msgid "なし"
msgstr ""

#: views\globalKeyConfig.py:61
#, python-format
msgid ""
"以下の項目において、重複するキー %(key)s が設定されています。\n"
"\n"
"%(command)s"
msgstr ""

#: views\globalKeyConfig.py:74
msgid "ショートカット1"
msgstr ""

#: views\globalKeyConfig.py:74
msgid "ショートカット2"
msgstr ""

#: views\globalKeyConfig.py:74
msgid "ショートカット3"
msgstr ""

#: views\globalKeyConfig.py:74
msgid "ショートカット4"
msgstr ""

#: views\globalKeyConfig.py:74
msgid "ショートカット5"
msgstr ""

#: views\globalKeyConfig.py:75 views\settingsDialog.py:36
msgid "設定"
msgstr ""

#: views\globalKeyConfig.py:80
msgid "登録内容の入力"
msgstr ""

#: views\globalKeyConfig.py:122
msgid "このショートカットは変更できません。"
msgstr ""

#: views\globalKeyConfig.py:127 views\globalKeyConfig.py:130
msgid "設定完了"
msgstr ""

#: views\globalKeyConfig.py:127
msgid "解除しました。"
msgstr ""

#: views\globalKeyConfig.py:130
#, python-format
msgid "%s に設定しました。"
msgstr ""

#: views\keyConfig.py:31
msgid "キー設定"
msgstr ""

#: views\keyConfig.py:39
msgid ""
"設定するには、使用したいキーの組み合わせを押します。\n"
"設定を解除するには、Escキーを押します。"
msgstr ""

#: views\keyConfig.py:45
msgid "設定解除"
msgstr ""

#: views\KeyValueSettingDialogBase.py:52
msgid "現在の登録内容"
msgstr ""

#: views\KeyValueSettingDialogBase.py:75
msgid "追加(&A)"
msgstr ""

#: views\KeyValueSettingDialogBase.py:76
msgid "変更(&M)"
msgstr ""

#: views\KeyValueSettingDialogBase.py:78
msgid "削除(&D)"
msgstr ""

#: views\KeyValueSettingDialogBase.py:84 views\KeyValueSettingDialogBase.py:278
#: views\SimpleInputDialog.py:55
msgid "ＯＫ"
msgstr ""

#: views\KeyValueSettingDialogBase.py:126
#: views\KeyValueSettingDialogBase.py:156
#, python-format
msgid "この%sは既に登録されています。登録を上書きしますか？"
msgstr ""

#: views\KeyValueSettingDialogBase.py:126
#: views\KeyValueSettingDialogBase.py:156
msgid "上書き確認"
msgstr ""

#: views\KeyValueSettingDialogBase.py:301
#, python-format
msgid "%sを空白や半角の=を含む値に設定することはできません。"
msgstr ""

#: views\KeyValueSettingDialogBase.py:341
#, python-format
msgid "%(v1)sと%(v2)sに同じショートカットキー%(key)sが設定されています。\n"
msgstr ""

#: views\KeyValueSettingDialogBase.py:344
#, python-format
msgid ""
"%(v1)s、%(v2)sなど計%(count)d箇所に同じショートカットキー%(key)sが設定されて"
"います。\n"
msgstr ""

#: views\KeyValueSettingDialogBase.py:348
#, python-format
msgid ""
"設定されたショートカット%sが認識できません。お手数ですが、このエラーメッセー"
"ジを作者へご連絡ください。\n"
msgstr ""

#: views\KeyValueSettingDialogBase.py:350
#, python-format
msgid ""
"%(command)sに設定されたショートカットキー%(key)sは、別の場所で利用されていま"
"す。\n"
msgstr ""

#: views\main.py:48
msgid "動作履歴"
msgstr ""

#: views\main.py:49 views\ydlManageLists.py:20 views\ydlManageLists.py:60
msgid "タイトル"
msgstr ""

#: views\main.py:50
msgid "詳細"
msgstr ""

#: views\main.py:51 views\main.py:56
msgid "サービス"
msgstr ""

#: views\main.py:52
msgid "日時"
msgstr ""

#: views\main.py:55
msgid "動作状況"
msgstr ""

#: views\main.py:57
msgid "状態"
msgstr ""

#: views\main.py:59
msgid "準備完了"
msgstr ""

#: views\main.py:59
#, python-format
msgid "%sを起動しました。"
msgstr ""

#: views\main.py:87
msgid ""
"で設定されたホットキーが正しくありません。キーの重複、存在しないキー名の指"
"定、使用できないキーパターンの指定などが考えられます。以下のキーの設定内容を"
"ご確認ください。\n"
"\n"
msgstr ""

#: views\main.py:163
msgid "ファイル(&F)"
msgstr ""

#: views\main.py:164
msgid "サービス(&S)"
msgstr ""

#: views\main.py:165
msgid "オプション(&O)"
msgstr ""

#: views\main.py:166
msgid "ヘルプ(&H)"
msgstr ""

#: views\main.py:211 views\main.py:217 views\main.py:295 views\main.py:453
#: views\settingsDialog.py:167
msgid ""
"設定の保存に失敗しました。下記のファイルへのアクセスが可能であることを確認し"
"てください。"
msgstr ""

#: views\main.py:229 views\main.py:299
msgid "URLを入力"
msgstr ""

#: views\main.py:229
msgid "再生ページのURL"
msgstr ""

#: views\main.py:236 views\main.py:243
msgid "ユーザ名を入力"
msgstr ""

#: views\main.py:236 views\main.py:243 views\tcManageUser.py:20
#: views\tcManageUser.py:99
msgid "ユーザ名"
msgstr ""

#: views\main.py:259
msgid "すでに削除されています。"
msgstr ""

#: views\main.py:261
msgid "アクセストークンの削除"
msgstr ""

#: views\main.py:261
msgid ""
"ツイキャス連携機能を無効化し、アクセストークンを削除します。よろしいですか？"
msgstr ""

#: views\main.py:268 views\main.py:396 views\main.py:402
msgid "完了"
msgstr ""

#: views\main.py:268
msgid "アクセストークンを削除しました。"
msgstr ""

#: views\main.py:299
msgid "URLの指定"
msgstr ""

#: views\main.py:332
msgid "グローバルホットキーの設定"
msgstr ""

#: views\main.py:371 views\main.py:393
msgid "確認"
msgstr ""

#: views\main.py:371
msgid ""
"録画処理を実行中です。このまま終了すると、録画は中断されます。終了してもよろ"
"しいですか？"
msgstr ""

#: views\main.py:393
msgid "Windows起動時の自動起動はすでに設定されています。設定を解除しますか？"
msgstr ""

#: views\main.py:396
msgid "Windows起動時の自動起動を無効化しました。"
msgstr ""

#: views\main.py:402
msgid "Windows起動時の自動起動を設定しました。"
msgstr ""

#: views\main.py:423
msgid "yt-dlp"
msgstr ""

#: views\settingsDialog.py:25
msgid "標準"
msgstr ""

#: views\settingsDialog.py:26
msgid "ダーク"
msgstr ""

#: views\settingsDialog.py:47
msgid "カテゴリ選択"
msgstr ""

#: views\settingsDialog.py:50
msgid "一般"
msgstr ""

#: views\settingsDialog.py:51
msgid "起動時にウィンドウを隠す(&H)"
msgstr ""

#: views\settingsDialog.py:52
msgid "終了時にタスクトレイに最小化(&M)"
msgstr ""

#: views\settingsDialog.py:55
msgid "表示/言語"
msgstr ""

#: views\settingsDialog.py:56
msgid "言語(&L)"
msgstr ""

#: views\settingsDialog.py:57
msgid "画面表示モード(&D)"
msgstr ""

#: views\settingsDialog.py:60
msgid "通知"
msgstr ""

#: views\settingsDialog.py:61 views\tcManageUser.py:23
#: views\tcManageUser.py:102
msgid "バルーン通知"
msgstr ""

#: views\settingsDialog.py:62 views\settingsDialog.py:71
#: views\tcManageUser.py:24 views\tcManageUser.py:103
msgid "録画"
msgstr ""

#: views\settingsDialog.py:63 views\tcManageUser.py:25
#: views\tcManageUser.py:104
msgid "ブラウザで開く"
msgstr ""

#: views\settingsDialog.py:64 views\tcManageUser.py:26
#: views\tcManageUser.py:105
msgid "サウンドを再生"
msgstr ""

#: views\settingsDialog.py:66 views\tcManageUser.py:27
#: views\tcManageUser.py:106
msgid "再生するサウンド"
msgstr ""

#: views\settingsDialog.py:68 views\settingsDialog.py:75
#: views\tcManageUser.py:108
msgid "参照"
msgstr ""

#: views\settingsDialog.py:73
msgid "保存先フォルダ(&F)"
msgstr ""

#: views\settingsDialog.py:77
msgid "ユーザごとにサブフォルダを作成(&S)"
msgstr ""

#: views\settingsDialog.py:78
msgid "保存ファイル名(&N)"
msgstr ""

#: views\settingsDialog.py:82
msgid "ネットワーク"
msgstr ""

#: views\settingsDialog.py:83
msgid "起動時に更新を確認(&U)"
msgstr ""

#: views\settingsDialog.py:84
msgid "プロキシサーバーの情報を手動で設定する(&M)"
msgstr ""

#: views\settingsDialog.py:85
msgid "サーバーURL"
msgstr ""

#: views\settingsDialog.py:87
msgid "ポート番号"
msgstr ""

#: views\settingsDialog.py:171 views\tcManageUser.py:154
msgid "効果音ファイルを選択"
msgstr ""

#: views\settingsDialog.py:171 views\tcManageUser.py:154
msgid "音声ファイル（.wav/.mp3/.ogg）"
msgstr ""

#: views\settingsDialog.py:179
msgid "保存先フォルダを選択"
msgstr ""

#: views\SimpleInputDialog.py:87 views\SimpleInputDialog.py:96
msgid "入力内容に誤りがあります。"
msgstr ""

#: views\SimpleInputDialog.py:87
#, python-format
msgid "行: %d"
msgstr ""

#: views\SimpleInputDialog.py:92
msgid "有効な値が入力されていません。"
msgstr ""

#: views\tcManageUser.py:19 views\tcManageUser.py:98
msgid "ユーザID"
msgstr ""

#: views\tcManageUser.py:22
msgid "専用設定"
msgstr ""

#: views\tcManageUser.py:58
msgid "有効"
msgstr ""

#: views\tcManageUser.py:58
msgid "無効"
msgstr ""

#: views\tcManageUser.py:61
msgid "ユーザの管理"
msgstr ""

#: views\tcManageUser.py:101
msgid "専用設定を使用"
msgstr ""

#: views\tcManageUser.py:113
msgid "通知設定"
msgstr ""

#: views\tcManageUser.py:120
msgid "ユーザ名が入力されていません。"
msgstr ""

#: views\updateDialog.py:23
#, python-format
msgid "アップデート - %s"
msgstr ""

#: views\updateDialog.py:32
#, python-format
msgid "version %s にアップデートできます。"
msgstr ""

#: views\updateDialog.py:33
msgid "進行状況"
msgstr ""

#: views\updateDialog.py:35
#, python-format
msgid "version %s アップデート情報"
msgstr ""

#: views\updateDialog.py:41
msgid "緊急のお知らせがあります。"
msgstr ""

#: views\updateDialog.py:44
msgid "お知らせページへ"
msgstr ""

#: views\updateDialog.py:59
msgid "アップデートをダウンロード中..."
msgstr ""

#: views\updateDialog.py:66
msgid ""
"updater.exeが見つかりませんでした。誤って削除したかなどをご確認ください。"
msgstr ""

#: views\versionDialog.py:21
msgid "バージョン情報"
msgstr ""

#: views\versionDialog.py:30
msgid "ソフトウェアバージョン"
msgstr ""

#: views\versionDialog.py:31
msgid "アップデータバージョン"
msgstr ""

#: views\versionDialog.py:33
msgid "ライセンス"
msgstr ""

#: views\versionDialog.py:34
msgid "開発元"
msgstr ""

#: views\versionDialog.py:35
msgid "ソフトウェア詳細情報"
msgstr ""

#: views\versionDialog.py:37
msgid ""
"ライセンス/著作権情報については、同梱の license.txt を参照してください。"
msgstr ""

#: views\versionDialog.py:49
msgid "閉じる"
msgstr ""

#: views\ydlManageLists.py:19 views\ydlManageLists.py:59
msgid "ID"
msgstr ""

#: views\ydlManageLists.py:21 views\ydlManageLists.py:61
msgid "URL"
msgstr ""

#: views\ydlManageLists.py:22
msgid "ダウンロード間隔"
msgstr ""

#: views\ydlManageLists.py:35
msgid "一括ダウンロードURLの管理"
msgstr ""

#: views\ydlManageLists.py:62
msgid "ダウンロード間隔（秒）"
msgstr ""

#: views\ydlManageLists.py:69
msgid "登録内容編集"
msgstr ""

#: views\ydlManageLists.py:77
msgid "ダウンロード間隔には数値（秒数）を指定してください。"
msgstr ""

#: views\ydlManageLists.py:88
#, python-format
msgid ""
"不正なURLが入力されました。\n"
"詳細：%s"
msgstr ""

#: views\ydlManageLists.py:92
msgid ""
"この機能では、動画のURLを直接指定することができません。プレイリストやチャンネ"
"ルページのURLを入力してください。"
msgstr ""
