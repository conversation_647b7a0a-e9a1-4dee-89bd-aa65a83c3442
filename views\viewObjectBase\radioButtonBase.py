#radioButtonBase for ViewCreator
#Copyright (C) 2019-2020 <PERSON><PERSON><PERSON> <<EMAIL>>


import wx
from views.viewObjectBase import viewObjectUtil, controlBase

class radioButton(controlBase.controlBase, wx.RadioButton):
    def __init__(self, *pArg, **kArg):
        self.focusFromKbd = viewObjectUtil.popArg(kArg, "enableTabFocus", True) #キーボードフォーカスの初期値
        return super().__init__(*pArg, **kArg)
