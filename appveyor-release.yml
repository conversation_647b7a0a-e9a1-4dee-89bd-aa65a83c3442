version: development.{build}-{branch}
branches:
  only:
    - release

skip_branch_with_pr: true

environment:
  matrix:
    # For Python versions available on Appveyor, see
    # http://www.appveyor.com/docs/installed-software#python
    # The list here is complete (excluding Python 2.6, which
    # isn't covered by this document) at the time of writing.

    - PYTHON: "C:\\Python311"

install:
  # We need wheel installed to build wheels
  - "%PYTHON%\\python.exe -m pip install -r requirements.txt"

build: off

test_script:
  # Put your test command here.
  # If you don't need to build C extensions on 64-bit Python 3.3 or 3.4,
  # you can remove "build.cmd" from the front of the command, as it's
  # only needed to support those cases.
  # Note that you must use the environment variable %PYTHON% to refer to
  # the interpreter you're using - Appveyor does not do anything special
  # to put the Python evrsion you want to use on PATH.
  - "echo Skipped Tests"

after_test:
  # This step builds your wheels.
  # Again, you only need build.cmd if you're building C extensions for
  # 64-bit Python 3.3/3.4. And you need to use %PYTHON% to get the correct
  # interpreter
  - "%PYTHON%\\python.exe tools\\build.py --appveyor"

artifacts:
  - path: ULTRA-*.zip
  - path: ULTRA-*.json

deploy:
  - provider: GitHub
    description: "ULTRA official release"
    auth_token:
      secure: iNSx8v6c+0TP9IyckinrvNWuX+hc7SKruOqRDl9GxdX59sZ2yqB8+UDAtqdPzKIg
    artifact: /(ULTRA-.*\.zip)|(ULTRA-.*\.json)/
    draft: true
    prerelease: false
    on:
      APPVEYOR_REPO_TAG: true
