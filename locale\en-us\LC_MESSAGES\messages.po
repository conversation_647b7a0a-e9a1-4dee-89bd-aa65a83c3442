# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the TCV package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: ULTRA\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-31 21:57+0900\n"
"PO-Revision-Date: 2025-07-31 21:59+0900\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.5\n"

#: app.py:66 menuItemsDic.py:31 menuItemsDic.py:36
msgid "録画形式の設定"
msgstr "Recording format settings"

#: app.py:66
#, python-format
msgid ""
"%(source)sの録画形式として%(ext)s形式が使用できなくなりました。規定値"
"の%(ext_default)s形式に変更します。"
msgstr ""
"The %(ext)s format is no longer available as a recording format for "
"%(source)s. Change to the default %(ext_default)s format."

#: AppBase.py:51
msgid ""
"ログ機能の初期化に失敗しました。下記のファイルへのアクセスが可能であることを"
"確認してください。"
msgstr ""
"Failed to initialize logger. Make sure you have permission to access to the "
"following file:"

#: AppBase.py:60
msgid "音声エンジンエラー"
msgstr "Speech Synthesizer Error"

#: AppBase.py:60
msgid ""
"音声読み上げ機能の初期化に失敗したため、読み上げ機能を使用できません。出力先"
"の変更をお試しください。"
msgstr ""
"Failed to initialize speech function. You cannot use speech. Please try to "
"change speech output."

#: keymapHandlerBase.py:696 keymapHandlerBase.py:726
#, python-format
msgid "%s は存在しないキーです。"
msgstr "%s does not exist."

#: keymapHandlerBase.py:767
#, python-format
msgid "%s は使用できないキーです。"
msgstr "%s is unusable key."

#: keymapHandlerBase.py:776
msgid "修飾キーのみのパターンは設定できません。"
msgstr "Keystroke which consists of only modifier keys cannot be added."

#: keymapHandlerBase.py:778
msgid "このキーは修飾キーと合わせて指定する必要があります。"
msgstr "This key must be added with some modifier keys."

#: keymapHandlerBase.py:783
msgid "修飾キーでないキーを複数指定することはできません。"
msgstr "This key combination is not supported."

#: keymapHandlerBase.py:786 keymapHandlerBase.py:794
msgid "このキーは、SHIFTキー以外の修飾キーと合わせて指定する必要があります。"
msgstr "This key cannot be added with Shift key."

#: keymapHandlerBase.py:789
msgid "修飾キーのみの組み合わせは指定できません。"
msgstr "Keystroke which consists of only modifier keys cannot be added."

#: keymapHandlerBase.py:798
msgid "この組み合わせは別の用途で予約されているため、利用できません。"
msgstr "This keystroke is already reserved."

#: menuItemsDic.py:15
msgid "ウィンドウを表示(&S)"
msgstr "&Show Window"

#: menuItemsDic.py:16
msgid "ウィンドウを隠す(&H)"
msgstr "&Hide Window"

#: menuItemsDic.py:17
msgid "終了(&X)"
msgstr "E&xit"

#: menuItemsDic.py:18
msgid "ツイキャス(&T)"
msgstr "&TwitCasting"

#: menuItemsDic.py:19
msgid "ツイキャス連携機能を有効化(&E)"
msgstr "&Enable TwitCasting Functions"

#: menuItemsDic.py:20
msgid "録画時にコメントをテキストファイルに保存する "
msgstr "Save Comments In Text Files When Recording"

#: menuItemsDic.py:21 sources\twitcasting.py:236
msgid "ログイン状態で録画"
msgstr "Record while logged in"

#: menuItemsDic.py:22
msgid "ユーザ情報を更新(&U)"
msgstr "&Update User Information"

#: menuItemsDic.py:23
msgid "ユーザを一括追加(&B)"
msgstr "Add Users in &Bulk"

#: menuItemsDic.py:24
msgid "過去ライブのダウンロード(&A)"
msgstr "Download &Archive"

#: menuItemsDic.py:25
msgid "過去ライブの一括ダウンロード(&D)"
msgstr "&Download All Archives"

#: menuItemsDic.py:26
msgid "指定したユーザのライブを録画(&R)"
msgstr "&Record live for the specified user"

#: menuItemsDic.py:27
msgid "ログインセッションを削除"
msgstr "Delete Login Session"

#: menuItemsDic.py:28
msgid "アクセストークンを削除"
msgstr "Delete access token"

#: menuItemsDic.py:29
msgid "アクセストークンを設定(&T)"
msgstr "Set up access &token"

#: menuItemsDic.py:30
msgid "通知対象ユーザの管理(&M)"
msgstr "&Manage Notified Users"

#: menuItemsDic.py:32
msgid "その他のサービス（&yt-dlp）"
msgstr "Other Services(&yt-dlp)"

#: menuItemsDic.py:33
msgid "一括ダウンロードを有効化(&E)"
msgstr "&Enable bulk download"

#: menuItemsDic.py:34
msgid "&URLを指定してダウンロード"
msgstr "Download by &URL"

#: menuItemsDic.py:35
msgid "一括ダウンロードURLの管理(&M)"
msgstr "&Manage bulk download URLs"

#: menuItemsDic.py:37
msgid "設定(&S)"
msgstr "&Settings"

#: menuItemsDic.py:38
msgid "キーボードショートカットの設定(&K)"
msgstr "Shortcut &Key Configuration"

#: menuItemsDic.py:39
msgid "グローバルホットキーの設定(&H)"
msgstr "Global &Hot Key Configuration"

#: menuItemsDic.py:40
msgid "Windows起動時の自動起動を有効化(&W)"
msgstr "Enable automatic startup on &Windows startup"

#: menuItemsDic.py:41
msgid "最新バージョンを確認(&U)"
msgstr "Check For &Updates"

#: menuItemsDic.py:42
msgid "バージョン情報(&V)"
msgstr "&Version Info"

#: notificationHandler.py:52
#, python-format
msgid "配信開始：%s、サービス：%s"
msgstr "Broadcasting Started: %s, service: %s"

#: recorder.py:198 recorder.py:200 recorder.py:211 recorder.py:237
#: recorder.py:241 recorder.py:243
msgid "録画エラー"
msgstr "Recording error"

#: recorder.py:198
msgid ""
"録画の開始に失敗しました。録画の保存先が適切に設定されていることを確認してく"
"ださい。定期的に再試行する場合は[はい]、処理を中断する場合は[いいえ]を選択し"
"てください。[はい]を選択して録画の保存先を変更することで、正しく録画を開始で"
"きる場合があります。"
msgstr ""
"Recording start failed. Make sure that the recording destination is set up "
"properly. Select Yes if you want to retry regularly, or No if you want to "
"interrupt the process. You may be able to start recording correctly by "
"selecting Yes and changing where the recording is saved."

#: recorder.py:200 recorder.py:211
#, python-format
msgid "%sのライブの録画処理を中断しました。"
msgstr "Recording process for %s's broadcasting has been canseled."

#: recorder.py:214
msgid "録画開始"
msgstr "Start recording"

#: recorder.py:214 recorder.py:245 sources\twitcasting.py:879
#: sources\twitcasting.py:891
#, python-format
msgid "ユーザ：%(user)s、ムービーID：%(movie)s"
msgstr "User: %(user)s, Movie ID:%(movie)s"

#: recorder.py:215
msgid "録画中"
msgstr "Recording"

#: recorder.py:237 recorder.py:241
#, python-format
msgid "%sのライブを録画中にエラーが発生しました。"
msgstr "An error has occured while recording %s's live."

#: recorder.py:237 recorder.py:241 recorder.py:243
#, python-format
msgid "詳細：%s"
msgstr "Details: %s"

#: recorder.py:243
#, python-format
msgid "%sのライブを録画中にエラーが発生したため、再度録画を開始します。"
msgstr "An error has occured while recording %s's live. retrying..."

#: recorder.py:245
msgid "録画終了"
msgstr "End of recording"

#: update.py:50 update.py:59 update.py:73 update.py:79 update.py:90
#: update.py:98 update.py:103 update.py:108 update.py:159
#: views\updateDialog.py:39
msgid "アップデート"
msgstr "update"

#: update.py:50
msgid "このバージョンではアップデートを使用できません。"
msgstr "You cannot use update with this version."

#: update.py:59
msgid ""
"アップデータが利用できません。お手数ですが、再度ソフトウェアをダウンロードし"
"てください。"
msgstr "Updater is not available. Please download this software again."

#: update.py:73
msgid "サーバーへの通信がタイムアウトしました。"
msgstr "Connection timed out."

#: update.py:79
msgid "サーバーへの接続に失敗しました。インターネット接続などをご確認ください"
msgstr "Connection failed. Please check your internet connection."

#: update.py:85
msgid "サーバーとの通信中に不明なエラーが発生しました。"
msgstr "Unknown error has occurred while connecting."

#: update.py:90
msgid "サーバーとの通信に失敗しました。"
msgstr "Connection failed."

#: update.py:98
msgid "現在のバージョンが最新です。アップデートの必要はありません。"
msgstr "This is the latest version."

#: update.py:103
msgid "リクエストパラメーターが不正です。開発者まで連絡してください"
msgstr ""
"Request parameter is invalid. Please contact the developer for further "
"assistance."

#: update.py:108
msgid "アップデーターが登録されていません。開発者に連絡してください。"
msgstr "Updater is not registered. Please contact the developer."

#: update.py:159
msgid ""
"ダウンロードが完了しました。\n"
"ソフトウェア終了時に、自動でアップデートされます。"
msgstr ""
"Download has successfully finished.\n"
"Update will be started when closing the application."

#: sources\twitcasting.py:42 sources\twitcasting.py:1159 views\main.py:421
msgid "ツイキャス"
msgstr "TwitCasting"

#: sources\twitcasting.py:45
msgid "動画（MP4）"
msgstr "Video (MP4)"

#: sources\twitcasting.py:46
msgid "動画（TS）"
msgstr "Video (TS)"

#: sources\twitcasting.py:47
msgid "音声のみ（MP3）"
msgstr "Audio Only (MP3)"

#: sources\twitcasting.py:61 sources\twitcasting.py:532
msgid "未接続"
msgstr "Disconnected"

#: sources\twitcasting.py:103
msgid "アクセストークンの有効期限が切れています"
msgstr "Access token has expired"

#: sources\twitcasting.py:103 sources\twitcasting.py:110
msgid ""
"本ソフトの使用を続けるには、アクセストークンを再度設定する必要があります。"
msgstr ""
"To continue using the software, you need to set the access token again."

#: sources\twitcasting.py:105
msgid ""
"アクセストークンの有効期限が切れています。本ソフトの使用を続けるには、アクセ"
"ストークンを再度設定する必要があります。"
msgstr ""
"The access token has expired. To continue using the software, you need to "
"set the access token again."

#: sources\twitcasting.py:110
msgid "アクセストークンの有効期限が近づいています"
msgstr "Access token is about to expire"

#: sources\twitcasting.py:112
msgid ""
"アクセストークンの有効期限が近づいています。本ソフトの使用を続けるには、アク"
"セストークンを再度設定する必要があります。"
msgstr ""
"The access token is about to expire. To continue using the software, you "
"need to set the access token again."

#: sources\twitcasting.py:130
msgid ""
"ログインセッションが不正なため、ログイン状態での録画機能を無効にしました。再"
"度この機能を使用するには、メニューの[ログイン状態で録画]を選択し、パスワード"
"を入力する必要があります。"
msgstr ""
"Due to an invalid login session, the recording function in the logged-in "
"state has been disabled. To use this feature again, you will need to select "
"Record while logged in in the menu and enter your password."

#: sources\twitcasting.py:199
msgid "配信開始"
msgstr "broadcasting started"

#: sources\twitcasting.py:208
msgid "録画対象の削除"
msgstr "Delete recording target"

#: sources\twitcasting.py:208 sources\twitcasting.py:209
#, python-format
msgid "%sのライブを、録画対象から削除しました。"
msgstr "%s has been deleted from recording target."

#: sources\twitcasting.py:231
msgid "録画警告"
msgstr "Recording Warning"

#: sources\twitcasting.py:231
msgid ""
"高画質版URLを取得できませんでした。録画データの画質が低下する可能性がありま"
"す。"
msgstr ""
"Could not retrieve the high quality version URL. The quality of recorded "
"data may be degraded."

#: sources\twitcasting.py:236
msgid ""
"ログイン状態で録画するためのURLを取得できませんでした。未ログイン状態と同じ"
"URLで続行します。"
msgstr ""
"Could not obtain URL for recording in logged-in state. Continue with the "
"same URL as the un-logged-in state."

#: sources\twitcasting.py:261
msgid "ユーザ名変更"
msgstr "Username changed"

#: sources\twitcasting.py:261 sources\twitcasting.py:264
#, python-format
msgid "「%(old)s」→「%(new)s」"
msgstr "%(old)s -> %(new)s"

#: sources\twitcasting.py:264
msgid "名前変更"
msgstr "name changed"

#: sources\twitcasting.py:279 sources\twitcasting.py:531
msgid "切断"
msgstr "disconnected"

#: sources\twitcasting.py:279
msgid "インターネット接続が切断されました。再試行します。"
msgstr "Your Internet connection has been disconnected. Trying again."

#: sources\twitcasting.py:280
msgid "接続試行中"
msgstr "Attempting to connect"

#: sources\twitcasting.py:314
msgid ""
"インターネット接続に失敗しました。現在ツイキャスとの連携機能を使用できませ"
"ん。"
msgstr ""
"Internet connection failed. The integration function with TwitCasting is not "
"available at this time."

#: sources\twitcasting.py:335
msgid ""
"認証に成功しました。このウィンドウを閉じて、アプリケーションに戻ってくださ"
"い。"
msgstr ""
"Authorization successful. Close this window and go back to the application."

#: sources\twitcasting.py:336
msgid "認証に失敗しました。もう一度お試しください。"
msgstr "Authorization failed.  Please try again."

#: sources\twitcasting.py:337
msgid ""
"しばらくしても画面が切り替わらない場合は、別のブラウザでお試しください。"
msgstr ""
"If the screen does not change after a while, open this page in another "
"browser."

#: sources\twitcasting.py:341
msgid "アカウントの追加"
msgstr "add account"

#: sources\twitcasting.py:350 sources\twitcasting.py:362
msgid "処理結果"
msgstr "result"

#: sources\twitcasting.py:350
msgid "キャンセルされました。"
msgstr "Canceled."

#: sources\twitcasting.py:362
msgid "認証が完了しました。"
msgstr "Authorization successful."

#: sources\twitcasting.py:404
msgid "ユーザ情報の保存に失敗しました。"
msgstr "Failed to save user information."

#: sources\twitcasting.py:412
msgid "接続完了"
msgstr "Connection completed"

#: sources\twitcasting.py:412
msgid "新着ライブの監視を開始しました。"
msgstr "We have started monitoring new lives."

#: sources\twitcasting.py:414
msgid "接続済み"
msgstr "Connected"

#: sources\twitcasting.py:446
msgid "指定したユーザが見つかりません。"
msgstr "The specified user could not be found."

#: sources\twitcasting.py:531
msgid "ツイキャスとの接続を切断しました。"
msgstr "Disconnected from TwitCasting."

#: sources\twitcasting.py:567
msgid "入力されたURLの形式が不正です。内容をご確認の上、再度お試しください。"
msgstr ""
"The URL entered is in an incorrect format. Please check the contents and try "
"again."

#: sources\twitcasting.py:570
msgid "過去ライブのダウンロード"
msgstr "Download Archive"

#: sources\twitcasting.py:570
msgid ""
"ライブ情報の取得に失敗しました。プレミア配信など、一部のユーザにしか閲覧でき"
"ないライブの場合、ULTRAと連携しているアカウントでログインすることで、ダウン"
"ロードに成功する可能性があります。今すぐログインしますか？"
msgstr ""
"Failed to get live information. In the case of a live show that is only "
"viewable by a limited number of users, such as a premier live, logging in "
"with an account connected to ULTRA may result in a successful download. Do "
"you want to log in now?"

#: sources\twitcasting.py:631
msgid "合い言葉の入力"
msgstr "Enter password"

#: sources\twitcasting.py:631
msgid "合い言葉"
msgstr "password"

#: sources\twitcasting.py:716
msgid "アクセストークンが見つかりません"
msgstr "Access token not found"

#: sources\twitcasting.py:716
msgid ""
"利用可能なアクセストークンが見つかりません。ブラウザを起動し、認証作業を行い"
"ますか？"
msgstr ""
"No available access tokens found. Do you want to start your browser and "
"perform authentication work?"

#: sources\twitcasting.py:726
msgid ""
"録画に失敗しました。録画が公開されていること、入力したURLに誤りがないことを確"
"認してください。合い言葉を入力した場合は、入力した合い言葉に誤りがないことを"
"確認してください。"
msgstr ""
"Recording failed. Make sure that the recording is public and that the URL "
"you entered is correct. If you type password, make sure that the follower "
"you typed is correct."

#: sources\twitcasting.py:735
msgid ""
"ツイキャスAPIの実行回数が上限に達しました。しばらくたってから、再度実行してく"
"ださい。"
msgstr ""
"The maximum number of runs of the TwitCasting API has been reached. Please "
"try again after a while."

#: sources\twitcasting.py:737
msgid ""
"ツイキャスAPIが500エラーを返しました。しばらく待ってから、再度接続してくださ"
"い。"
msgstr ""
"TwitCasting API returned a 500 error. Wait a while and try to connect again."

#: sources\twitcasting.py:739
msgid "現在ツイキャスとの連携機能を使用できません。開発者に連絡してください。"
msgstr "You cannot use TwitCasting functions. Please contact the developer."

#: sources\twitcasting.py:752
#, python-format
msgid "ツイキャスAPIとの通信中にエラーが発生しました。詳細：%s"
msgstr ""
"An error has occured during the communication between TwitCasting. Details: "
"%s"

#: sources\twitcasting.py:786
msgid "このユーザのライブはすでに録画中です。"
msgstr "This user's live is already recorded."

#: sources\twitcasting.py:791
msgid "このユーザはすでに登録されています。"
msgstr "This user is already registered."

#: sources\twitcasting.py:805
msgid "ユーザ名を指定して録画"
msgstr "Record with username"

#: sources\twitcasting.py:805
#, python-format
msgid ""
"%sを、録画対象として追加しました。この登録は一定時間経過後に自動で削除されま"
"す。"
msgstr ""
"%s added a recording target. This registration is automatically deleted "
"after a certain amount of time."

#: sources\twitcasting.py:828
msgid "対象ユーザの指定"
msgstr "specify users"

#: sources\twitcasting.py:829
msgid ""
"対象アカウントの@からはじまるアカウント名を、\n"
"改行区切りで入力してください。"
msgstr ""
"Enter the account name of the target account starting with @,\n"
"separated by line breaks."

#: sources\twitcasting.py:879
msgid "コメント保存開始"
msgstr "Start saving comments"

#: sources\twitcasting.py:891
msgid "コメント保存終了"
msgstr "End of comment save"

#: sources\twitcasting.py:998 sources\twitcasting.py:1001
#: sources\twitcasting.py:1006
msgid "ユーザ情報の更新"
msgstr "Update user information"

#: sources\twitcasting.py:998
msgid "ユーザ情報の更新を開始します。"
msgstr "Start updating user information."

#: sources\twitcasting.py:1001
#, python-format
msgid "%sの情報を取得しています。"
msgstr "getting the information of %s"

#: sources\twitcasting.py:1006
msgid "ユーザ情報の更新が終了しました。"
msgstr "The user information has been updated."

#: sources\twitcasting.py:1022
msgid "一括追加"
msgstr "Add in a lump"

#: sources\twitcasting.py:1026
msgid "処理を開始します。"
msgstr "Operation Started"

#: sources\twitcasting.py:1046
#, python-format
msgid "%sを追加しました。"
msgstr "Added %s."

#: sources\twitcasting.py:1053
msgid "処理が終了しました。"
msgstr "Operation finished."

#: sources\twitcasting.py:1098 sources\twitcasting.py:1101
#: sources\twitcasting.py:1105 sources\twitcasting.py:1110
#: sources\twitcasting.py:1113
msgid "一括録画"
msgstr "Record All"

#: sources\twitcasting.py:1098
msgid "ライブ一覧を取得しています。"
msgstr "Retrieving movie list."

#: sources\twitcasting.py:1101
#, python-format
msgid "処理を開始します。対象ライブ数：%i"
msgstr "Operation started. Number of movies: %i"

#: sources\twitcasting.py:1105
#, python-format
msgid "処理中（%(index)i/%(total)i）"
msgstr "processing (%(index)i/%(total)i)"

#: sources\twitcasting.py:1110
msgid "ファイルが既に存在するため、録画をスキップします。"
msgstr "Recording will be skipped because the file exists."

#: sources\twitcasting.py:1113
#, python-format
msgid "完了。%i件録画しました。"
msgstr "Operation finished. Downloaded %i movies."

#: sources\twitcasting.py:1156
msgid "Twitter"
msgstr "Twitter"

#: sources\twitcasting.py:1161
msgid ""
"ログインに対応しているのはTwitterとツイキャスのアカウントのみです。その他の"
"サービスでのログインはできません。"
msgstr ""
"Login is only available for Twitter and Twitcasting accounts. You cannot log "
"in with any other service."

#: sources\twitcasting.py:1164
#, python-format
msgid "%(service)sアカウント「%(account)s」のパスワードを入力"
msgstr "Enter the password for the %(service)s account \"%(account)s\""

#: sources\twitcasting.py:1165
msgid "パスワードの入力"
msgstr "Enter your password"

#: sources\twitcasting.py:1175 sources\twitcasting.py:1179
msgid "ログイン中にエラーが発生しました。"
msgstr "An error has occured during logging in."

#: sources\twitcasting.py:1176
msgid "パスワードが正しくありません。"
msgstr "The password is incorrect."

#: sources\twitcasting.py:1177
msgid ""
"パスワードが正しくありません。なお、ULTRA Version 1.5.2の更新に伴い、ツイキャ"
"スの「ログインパスワード」を使用してログインするようになりました。ツイキャス"
"のサイトでログインパスワードを設定し、そのパスワードをULTRAに設定する必要があ"
"ります。"
msgstr ""
"The password is incorrect. With the update of ULTRA Version 1.5.2, you can "
"now log in using your TwitCasting login password. You must set a login "
"password on the TwitCasting site and set that password to ULTRA."

#: sources\twitcasting.py:1178
msgid ""
"reCAPTCHAによる認証が必要です。ブラウザからTwitterにログインし、認証を行って"
"ください。"
msgstr ""
"ReCAPTCHA authentication is required. From your browser, login to the "
"twitter, and finish the authentication."

#: sources\twitcasting.py:1180
msgid "認証が必要です。ブラウザで操作を完了してください。"
msgstr ""
"Authorization is required. Open the browser, and finish the authentication."

#: sources\ydl.py:28
msgid "その他のサービス（yt-dlp）"
msgstr "Other Services(yt-dlp)"

#: sources\ydl.py:31
msgid "動画"
msgstr "video"

#: sources\ydl.py:32
msgid "音声のみ"
msgstr "Audio only"

#: sources\ydl.py:42 sources\ydl.py:185
msgid "一括ダウンロード無効"
msgstr "Bulk download disabled"

#: sources\ydl.py:46
msgid "一括ダウンロード有効"
msgstr "Bulk download enabled"

#: sources\ydl.py:90 sources\ydl.py:102 sources\ydl.py:116
msgid "ダウンロードエラー"
msgstr "Download Error"

#: sources\ydl.py:90 sources\ydl.py:92
#, python-format
msgid ""
"動画情報の取得に失敗しました。\n"
"詳細：%s"
msgstr ""
"Failed to get video information.\n"
"Details: %s"

#: sources\ydl.py:102 sources\ydl.py:104
msgid "このプレイリストは、一括ダウンロードURLとして登録されています。"
msgstr "This playlist is registered as a bulk download URL."

#: sources\ydl.py:116 sources\ydl.py:118
#, python-format
msgid "%sのダウンロードは現在サポートされていません。"
msgstr "Downloading %s is not currently supported."

#: sources\ydl.py:159
#, python-format
msgid ""
"プレイリストの取得に失敗しました。\n"
"詳細：%s"
msgstr ""
"Failed to get playlist.\n"
"Details: %s"

#: sources\ydl.py:171 sources\ydl.py:175 sources\ydl.py:179 sources\ydl.py:358
msgid "プレイリストの保存"
msgstr "Saving playlists"

#: sources\ydl.py:171
#, python-format
msgid "処理開始：%s"
msgstr "Start of processing: %s"

#: sources\ydl.py:175
#, python-format
msgid "処理終了：%s"
msgstr "End of processing: %s"

#: sources\ydl.py:179
#, python-format
msgid "処理中止：%s"
msgstr "Processing aborted: %s"

#: sources\ydl.py:358
#, python-format
msgid "処理中（%(title)s）：%(cnt)d/%(total)d"
msgstr "Processing (%(title)s): %(cnt)d/%(total)d"

#: views\auth.py:23
msgid "ブラウザでの操作を待っています..."
msgstr "waiting for browser..."

#: views\auth.py:24 views\KeyValueSettingDialogBase.py:85
#: views\KeyValueSettingDialogBase.py:279 views\settingsDialog.py:92
#: views\SimpleInputDialog.py:56 views\updateDialog.py:45
msgid "キャンセル"
msgstr "cancel"

#: views\base.py:69
msgid "現在メニューは操作できません"
msgstr "You cannot use the menu at the moment."

#: views\base.py:117
msgid ""
"で設定されたショートカットキーが正しくありません。キーの重複、存在しないキー"
"名の指定、使用できないキーパターンの指定などが考えられます。以下のキーの設定"
"内容をご確認ください。\n"
"\n"
msgstr ""
"keyboard shortcut is invalid. Please check for conflicting / unusable key "
"combinations and key names.\n"

#: views\base.py:120 views\fontManager.py:25 views\fontManager.py:50
#: views\globalKeyConfig.py:61 views\KeyValueSettingDialogBase.py:301
#: views\KeyValueSettingDialogBase.py:352 views\main.py:90
#: views\updateDialog.py:66
msgid "エラー"
msgstr "error"

#: views\fontManager.py:25
msgid ""
"設定されているフォント情報が不正です。デフォルトのフォントを適用します。"
msgstr "Font configuration is invalid. Default settings will be applied."

#: views\fontManager.py:50
msgid "原因不明のエラーにより、フォントの変更に失敗しました。"
msgstr "Font configuration has failed."

#: views\globalKeyConfig.py:20 views\globalKeyConfig.py:74
#: views\tcManageUser.py:21 views\tcManageUser.py:100
msgid "名前"
msgstr "name"

#: views\globalKeyConfig.py:21
msgid "ショートカット"
msgstr "shortcut"

#: views\globalKeyConfig.py:22 views\globalKeyConfig.py:74
msgid "識別子"
msgstr "identifier"

#: views\globalKeyConfig.py:31 views\main.py:324
msgid "ショートカットキーの設定"
msgstr "Shortcut Key Configuration"

#: views\globalKeyConfig.py:54 views\globalKeyConfig.py:71
#: views\globalKeyConfig.py:102 views\globalKeyConfig.py:107
#: views\globalKeyConfig.py:120 views\globalKeyConfig.py:128
#: views\globalKeyConfig.py:138 views\globalKeyConfig.py:143
#: views\keyConfig.py:57 views\KeyValueSettingDialogBase.py:337
#: views\main.py:429 views\main.py:448
msgid "なし"
msgstr "None"

#: views\globalKeyConfig.py:61
#, python-format
msgid ""
"以下の項目において、重複するキー %(key)s が設定されています。\n"
"\n"
"%(command)s"
msgstr ""
"%(key)s is configured in following items.\\n\n"
"\n"
"%(command)s"

#: views\globalKeyConfig.py:74
msgid "ショートカット1"
msgstr "shortcut 1"

#: views\globalKeyConfig.py:74
msgid "ショートカット2"
msgstr "shortcut 2"

#: views\globalKeyConfig.py:74
msgid "ショートカット3"
msgstr "shortcut 3"

#: views\globalKeyConfig.py:74
msgid "ショートカット4"
msgstr "shortcut 4"

#: views\globalKeyConfig.py:74
msgid "ショートカット5"
msgstr "shortcut 5"

#: views\globalKeyConfig.py:75 views\settingsDialog.py:36
msgid "設定"
msgstr "settings"

#: views\globalKeyConfig.py:80
msgid "登録内容の入力"
msgstr "register set contents"

#: views\globalKeyConfig.py:122
msgid "このショートカットは変更できません。"
msgstr "This shortcut cannot be modified."

#: views\globalKeyConfig.py:127 views\globalKeyConfig.py:130
msgid "設定完了"
msgstr "Setup has been completed"

#: views\globalKeyConfig.py:127
msgid "解除しました。"
msgstr "unregistered."

#: views\globalKeyConfig.py:130
#, python-format
msgid "%s に設定しました。"
msgstr "Registered as %s."

#: views\keyConfig.py:31
msgid "キー設定"
msgstr "Key Configuration"

#: views\keyConfig.py:39
msgid ""
"設定するには、使用したいキーの組み合わせを押します。\n"
"設定を解除するには、Escキーを押します。"
msgstr ""
"Press preferred key combination to register.\n"
"Press Esc to remove configuration."

#: views\keyConfig.py:45
msgid "設定解除"
msgstr "remove registered configuration"

#: views\KeyValueSettingDialogBase.py:52
msgid "現在の登録内容"
msgstr "Current Settings"

#: views\KeyValueSettingDialogBase.py:75
msgid "追加(&A)"
msgstr "&Add "

#: views\KeyValueSettingDialogBase.py:76
msgid "変更(&M)"
msgstr "&Modify"

#: views\KeyValueSettingDialogBase.py:78
msgid "削除(&D)"
msgstr "&Delete"

#: views\KeyValueSettingDialogBase.py:84 views\KeyValueSettingDialogBase.py:278
#: views\SimpleInputDialog.py:55
msgid "ＯＫ"
msgstr "OK"

#: views\KeyValueSettingDialogBase.py:126
#: views\KeyValueSettingDialogBase.py:156
#, python-format
msgid "この%sは既に登録されています。登録を上書きしますか？"
msgstr "This %s is already registered. Do you wish to overwrite?"

#: views\KeyValueSettingDialogBase.py:126
#: views\KeyValueSettingDialogBase.py:156
msgid "上書き確認"
msgstr "Overwriting Confirmation"

#: views\KeyValueSettingDialogBase.py:301
#, python-format
msgid "%sを空白や半角の=を含む値に設定することはできません。"
msgstr "%s cannot include space and equals sign."

#: views\KeyValueSettingDialogBase.py:341
#, python-format
msgid "%(v1)sと%(v2)sに同じショートカットキー%(key)sが設定されています。\n"
msgstr "%(v1)s and %(v2)s have same shortcut key %(key)s.\n"

#: views\KeyValueSettingDialogBase.py:344
#, python-format
msgid ""
"%(v1)s、%(v2)sなど計%(count)d箇所に同じショートカットキー%(key)sが設定されて"
"います。\n"
msgstr "%(key)s is registered at %(count)i items, %(v1)s, %(v2)s and more.\n"

#: views\KeyValueSettingDialogBase.py:348
#, python-format
msgid ""
"設定されたショートカット%sが認識できません。お手数ですが、このエラーメッセー"
"ジを作者へご連絡ください。\n"
msgstr ""
"Configured shortcut %s cannot be recognized. Please contact the developer "
"for further assistance.\n"

#: views\KeyValueSettingDialogBase.py:350
#, python-format
msgid ""
"%(command)sに設定されたショートカットキー%(key)sは、別の場所で利用されていま"
"す。\n"
msgstr ""
"The key combination %(key)s, configured as %(command)s, is reserved.\\n\n"

#: views\main.py:48
msgid "動作履歴"
msgstr "operation History"

#: views\main.py:49 views\ydlManageLists.py:20 views\ydlManageLists.py:60
msgid "タイトル"
msgstr "title"

#: views\main.py:50
msgid "詳細"
msgstr "Details"

#: views\main.py:51 views\main.py:56
msgid "サービス"
msgstr "Service"

#: views\main.py:52
msgid "日時"
msgstr "Date"

#: views\main.py:55
msgid "動作状況"
msgstr "Operating status"

#: views\main.py:57
msgid "状態"
msgstr "Condition"

#: views\main.py:59
msgid "準備完了"
msgstr "Ready"

#: views\main.py:59
#, python-format
msgid "%sを起動しました。"
msgstr "%s launched."

#: views\main.py:87
msgid ""
"で設定されたホットキーが正しくありません。キーの重複、存在しないキー名の指"
"定、使用できないキーパターンの指定などが考えられます。以下のキーの設定内容を"
"ご確認ください。\n"
"\n"
msgstr ""
"hotkey is invalid. Please check for conflicting / unusable key combinations "
"and key names.\n"
"\n"

#: views\main.py:163
msgid "ファイル(&F)"
msgstr "&File"

#: views\main.py:164
msgid "サービス(&S)"
msgstr "&Services"

#: views\main.py:165
msgid "オプション(&O)"
msgstr "&Options"

#: views\main.py:166
msgid "ヘルプ(&H)"
msgstr "&Help"

#: views\main.py:211 views\main.py:217 views\main.py:295 views\main.py:453
#: views\settingsDialog.py:167
msgid ""
"設定の保存に失敗しました。下記のファイルへのアクセスが可能であることを確認し"
"てください。"
msgstr ""
"Failed to save settings. Make sure you can access to the following files:"

#: views\main.py:229 views\main.py:299
msgid "URLを入力"
msgstr "Enter URL"

#: views\main.py:229
msgid "再生ページのURL"
msgstr "Playback page URL"

#: views\main.py:236 views\main.py:243
msgid "ユーザ名を入力"
msgstr "Enter user name"

#: views\main.py:236 views\main.py:243 views\tcManageUser.py:20
#: views\tcManageUser.py:99
msgid "ユーザ名"
msgstr "user name"

#: views\main.py:259
msgid "すでに削除されています。"
msgstr "It has already been deleted."

#: views\main.py:261
msgid "アクセストークンの削除"
msgstr "Delete access token"

#: views\main.py:261
msgid ""
"ツイキャス連携機能を無効化し、アクセストークンを削除します。よろしいですか？"
msgstr ""
"Disable TwitCasting functions and remove the access token. Are you sure?"

#: views\main.py:268 views\main.py:396 views\main.py:402
msgid "完了"
msgstr "Finish"

#: views\main.py:268
msgid "アクセストークンを削除しました。"
msgstr "Removed the access token."

#: views\main.py:299
msgid "URLの指定"
msgstr "set URL"

#: views\main.py:332
msgid "グローバルホットキーの設定"
msgstr "Global Hot Keys Settings"

#: views\main.py:371 views\main.py:393
msgid "確認"
msgstr "confirmation"

#: views\main.py:371
msgid ""
"録画処理を実行中です。このまま終了すると、録画は中断されます。終了してもよろ"
"しいですか？"
msgstr ""
"Recording process is running. If you exit, the recording will be "
"interrupted. Are you sure?"

#: views\main.py:393
msgid "Windows起動時の自動起動はすでに設定されています。設定を解除しますか？"
msgstr ""
"Automatic startup at Windows startup is already set. Do you want to un-set "
"it?"

#: views\main.py:396
msgid "Windows起動時の自動起動を無効化しました。"
msgstr "Disabled automatic startup at Windows startup."

#: views\main.py:402
msgid "Windows起動時の自動起動を設定しました。"
msgstr "Set automatic startup at Windows startup."

#: views\main.py:423
msgid "yt-dlp"
msgstr "yt-dlp"

#: views\settingsDialog.py:25
msgid "標準"
msgstr "normal"

#: views\settingsDialog.py:26
msgid "ダーク"
msgstr "Dark"

#: views\settingsDialog.py:47
msgid "カテゴリ選択"
msgstr "category"

#: views\settingsDialog.py:50
msgid "一般"
msgstr "general"

#: views\settingsDialog.py:51
msgid "起動時にウィンドウを隠す(&H)"
msgstr "&Hide window at startup"

#: views\settingsDialog.py:52
msgid "終了時にタスクトレイに最小化(&M)"
msgstr "&Minimize to task tray at exit"

#: views\settingsDialog.py:55
msgid "表示/言語"
msgstr "Display/Language"

#: views\settingsDialog.py:56
msgid "言語(&L)"
msgstr "&Language"

#: views\settingsDialog.py:57
msgid "画面表示モード(&D)"
msgstr "&Display mode"

#: views\settingsDialog.py:60
msgid "通知"
msgstr "notification"

#: views\settingsDialog.py:61 views\tcManageUser.py:23
#: views\tcManageUser.py:102
msgid "バルーン通知"
msgstr "Balloon notification"

#: views\settingsDialog.py:62 views\settingsDialog.py:71
#: views\tcManageUser.py:24 views\tcManageUser.py:103
msgid "録画"
msgstr "Record"

#: views\settingsDialog.py:63 views\tcManageUser.py:25
#: views\tcManageUser.py:104
msgid "ブラウザで開く"
msgstr "Open in browser"

#: views\settingsDialog.py:64 views\tcManageUser.py:26
#: views\tcManageUser.py:105
msgid "サウンドを再生"
msgstr "Play sound"

#: views\settingsDialog.py:66 views\tcManageUser.py:27
#: views\tcManageUser.py:106
msgid "再生するサウンド"
msgstr "Sounds to play"

#: views\settingsDialog.py:68 views\settingsDialog.py:75
#: views\tcManageUser.py:108
msgid "参照"
msgstr "browse"

#: views\settingsDialog.py:73
msgid "保存先フォルダ(&F)"
msgstr "Destination &folder"

#: views\settingsDialog.py:77
msgid "ユーザごとにサブフォルダを作成(&S)"
msgstr "Create &sub folders for each user"

#: views\settingsDialog.py:78
msgid "保存ファイル名(&N)"
msgstr "Save file &name"

#: views\settingsDialog.py:82
msgid "ネットワーク"
msgstr "network"

#: views\settingsDialog.py:83
msgid "起動時に更新を確認(&U)"
msgstr "Automatically Check for &Updates on startup"

#: views\settingsDialog.py:84
msgid "プロキシサーバーの情報を手動で設定する(&M)"
msgstr "&Manually configure proxy information"

#: views\settingsDialog.py:85
msgid "サーバーURL"
msgstr "server URL"

#: views\settingsDialog.py:87
msgid "ポート番号"
msgstr "port number"

#: views\settingsDialog.py:171 views\tcManageUser.py:154
msgid "効果音ファイルを選択"
msgstr "select a sound file"

#: views\settingsDialog.py:171 views\tcManageUser.py:154
msgid "音声ファイル（.wav/.mp3/.ogg）"
msgstr "Sound Files(.WAV/.MP3/.OGG)"

#: views\settingsDialog.py:179
msgid "保存先フォルダを選択"
msgstr "Select the folder to save to"

#: views\SimpleInputDialog.py:87 views\SimpleInputDialog.py:96
msgid "入力内容に誤りがあります。"
msgstr "There is an error in the information you entered."

#: views\SimpleInputDialog.py:87
#, python-format
msgid "行: %d"
msgstr "Line: %d"

#: views\SimpleInputDialog.py:92
msgid "有効な値が入力されていません。"
msgstr "You have not entered a valid value."

#: views\tcManageUser.py:19 views\tcManageUser.py:98
msgid "ユーザID"
msgstr "User ID"

#: views\tcManageUser.py:22
msgid "専用設定"
msgstr "Dedicated settings"

#: views\tcManageUser.py:58
msgid "有効"
msgstr "Enabled"

#: views\tcManageUser.py:58
msgid "無効"
msgstr "Disabled"

#: views\tcManageUser.py:61
msgid "ユーザの管理"
msgstr "Manage users"

#: views\tcManageUser.py:101
msgid "専用設定を使用"
msgstr "Use dedicated settings"

#: views\tcManageUser.py:113
msgid "通知設定"
msgstr "Notification settings"

#: views\tcManageUser.py:120
msgid "ユーザ名が入力されていません。"
msgstr "The user name has not been entered."

#: views\updateDialog.py:23
#, python-format
msgid "アップデート - %s"
msgstr "update - %s"

#: views\updateDialog.py:32
#, python-format
msgid "version %s にアップデートできます。"
msgstr "Version %s is now available."

#: views\updateDialog.py:33
msgid "進行状況"
msgstr "progress"

#: views\updateDialog.py:35
#, python-format
msgid "version %s アップデート情報"
msgstr "version %s update information"

#: views\updateDialog.py:41
msgid "緊急のお知らせがあります。"
msgstr "There is an important information."

#: views\updateDialog.py:44
msgid "お知らせページへ"
msgstr "Go to the information page"

#: views\updateDialog.py:59
msgid "アップデートをダウンロード中..."
msgstr "Downloading update..."

#: views\updateDialog.py:66
msgid ""
"updater.exeが見つかりませんでした。誤って削除したかなどをご確認ください。"
msgstr "Updater.exe is not found."

#: views\versionDialog.py:21
msgid "バージョン情報"
msgstr "version information"

#: views\versionDialog.py:30
msgid "ソフトウェアバージョン"
msgstr "software version"

#: views\versionDialog.py:31
msgid "アップデータバージョン"
msgstr "updater version"

#: views\versionDialog.py:33
msgid "ライセンス"
msgstr "license"

#: views\versionDialog.py:34
msgid "開発元"
msgstr "developers"

#: views\versionDialog.py:35
msgid "ソフトウェア詳細情報"
msgstr "software details"

#: views\versionDialog.py:37
msgid ""
"ライセンス/著作権情報については、同梱の license.txt を参照してください。"
msgstr "See the included license.txt for licensing / copyright information."

#: views\versionDialog.py:49
msgid "閉じる"
msgstr "close"

#: views\ydlManageLists.py:19 views\ydlManageLists.py:59
msgid "ID"
msgstr "ID"

#: views\ydlManageLists.py:21 views\ydlManageLists.py:61
msgid "URL"
msgstr "URL"

#: views\ydlManageLists.py:22
msgid "ダウンロード間隔"
msgstr "Download interval"

#: views\ydlManageLists.py:35
msgid "一括ダウンロードURLの管理"
msgstr "Managing bulk download URLs"

#: views\ydlManageLists.py:62
msgid "ダウンロード間隔（秒）"
msgstr "Download Interval (seconds)"

#: views\ydlManageLists.py:69
msgid "登録内容編集"
msgstr "Edit registration details"

#: views\ydlManageLists.py:77
msgid "ダウンロード間隔には数値（秒数）を指定してください。"
msgstr "Specify a number (number of seconds) for the download interval."

#: views\ydlManageLists.py:88
#, python-format
msgid ""
"不正なURLが入力されました。\n"
"詳細：%s"
msgstr ""
"An invalid URL was entered.\n"
"Details: %s"

#: views\ydlManageLists.py:92
msgid ""
"この機能では、動画のURLを直接指定することができません。プレイリストやチャンネ"
"ルページのURLを入力してください。"
msgstr ""
"This feature does not allow you to specify the URL of the video directly. "
"Enter the URL of a playlist or channel page."

#~ msgid "再接続"
#~ msgstr "reconnect"

#~ msgid "ツイキャスとの接続が切断されたため、再度接続します。"
#~ msgstr "Connection to twitcasting has been deleted. Retrying."

#~ msgid "Twitterアカウント認証"
#~ msgstr "Twitter Account authentication"

#~ msgid ""
#~ "Twitterからフォローリストを取得できませんでした。指定したユーザが存在しな"
#~ "いか、フォローしていない非公開アカウントである可能性があります。"
#~ msgstr ""
#~ "Failed to get a follow list from Twitter. The specified user may not "
#~ "exist or may be a private account that you do not follow."

#~ msgid ""
#~ "Twitterからフォローリストを取得できませんでした。しばらくたってから再度お"
#~ "試しください。状況が改善しない場合には、開発者までお問い合わせください。"
#~ msgstr ""
#~ "Failed to get a follow list from Twitter. Please try again after a while. "
#~ "If the situation does not improve, contact the developer."

#~ msgid "Twitterアカウントを選択"
#~ msgstr "Select a Twitter account"

#~ msgid "アカウント"
#~ msgstr "account"

#~ msgid "Twitter スペース"
#~ msgstr "Twitter Spaces"

#~ msgid "Twitter スペース(&S)"
#~ msgstr "Twitter &Spaces"

#~ msgid "Twitter スペースとの連携機能を有効化(&E)"
#~ msgstr "&Enable integration with Twitter Spaces"

#~ msgid "フォロー中のユーザをすべて追加(&F)"
#~ msgstr "Add all users you &follow"

#~ msgid "ユーザ情報の更新(&I)"
#~ msgstr "Update user &information"

#~ msgid "スペースの&URLを指定して録画"
#~ msgstr "Record By &URL"

#~ msgid "連携アカウントの管理(&A)"
#~ msgstr "Manage Linked &Accounts"

#~ msgid "MP3形式"
#~ msgstr "MP3 format"

#~ msgid "Twitterアカウントの連携"
#~ msgstr "Link Twitter Accounts"

#~ msgid ""
#~ "Twitter スペースを使用する前に、使用するTwitterアカウントを設定する必要が"
#~ "あります。今すぐ設定画面を開きますか？"
#~ msgstr ""
#~ "Before you use Twitter space, you'll need to set up a Twitter account to "
#~ "use. Do you want to open the settings screen now?"

#~ msgid ""
#~ "Twitterアカウント情報の読み込みに失敗しました。再度アカウントの連携を行っ"
#~ "てください。今すぐ設定画面を開きますか？"
#~ msgstr ""
#~ "Failed to load Twitter account information. Please link your account "
#~ "again. Do you want to open the settings screen now?"

#~ msgid "フォロー中のユーザの追加が完了しました。追加件数：%d"
#~ msgstr "You have added users you are following. Number of additions: %d"

#~ msgid "フォロー中のユーザの取得に失敗しました。詳細:%s"
#~ msgstr "Failed to retrieve user being followed. Details: %s"

#~ msgid "スペースの監視を開始しました。"
#~ msgstr "You have started monitoring Spaces."

#~ msgid "ユーザ情報の更新に失敗しました。詳細:%s"
#~ msgstr "Failed to update user information. Details: %s"

#~ msgid "以下のユーザの情報を取得できませんでした。"
#~ msgstr "The following users could not obtain information:"

#~ msgid "ユーザ情報の更新が完了しました。"
#~ msgstr "The user information has been updated."

#~ msgid "Twitterとの接続を切断しました。"
#~ msgstr "You have disconnected from Twitter."

#~ msgid "Twitter エラー"
#~ msgstr "Twitter Error"

#~ msgid "Twitterとの通信中にエラーが発生しました。詳細：%s"
#~ msgstr "An error occurred while communicating with Twitter. Details: %s"

#~ msgid ""
#~ "Twitterとの接続に失敗しました。インターネット接続に問題がない場合は、しば"
#~ "らくたってから再度お試しください。この問題が再度発生する場合は、開発者まで"
#~ "お問い合わせください。"
#~ msgstr ""
#~ "Connection with Twitter failed. If you're connected to the Internet, try "
#~ "again later. If you experience this issue again, contact to the developer."

#~ msgid "Twitterからの応答が不正です。開発者までご連絡ください。"
#~ msgstr ""
#~ "The response from Twitter is incorrect. Please contact the developer."

#~ msgid ""
#~ "Twitterアカウントの認証情報が正しくありません。再度アカウントの連携を行っ"
#~ "てください。今すぐ設定画面を開きますか？"
#~ msgstr ""
#~ "Your Twitter account credentials are incorrect. Please link your account "
#~ "again. Do you want to open the settings screen now?"

#~ msgid "Twitterとの通信に失敗しました。詳細：%s"
#~ msgstr "Communication with Twitter failed. Details: %s"

#~ msgid "認証情報の保存に失敗しました。"
#~ msgstr "Failed to save credentials."

#~ msgid "%(field)s変更"
#~ msgstr "%(field)s changed"

#~ msgid ""
#~ "フォロー中のユーザを取得するアカウントの@からはじまるアカウント名を入力し"
#~ "てください。\n"
#~ "後悔アカウント、認証に用いたアカウント、\n"
#~ "または認証に用いたアカウントがフォローしている非公開アカウントを指定できま"
#~ "す。"
#~ msgstr ""
#~ "Enter the account name that starts with @, the account you want to get "
#~ "following users for.\n"
#~ "Regret accounts, accounts used for authentication,\n"
#~ "Or you can specify a private account that the account you used to "
#~ "authenticate follows."

#~ msgid "スペースのURL"
#~ msgstr "Space URL"

#~ msgid "このスペースは既に終了しています。"
#~ msgstr "This space has already ended."

#~ msgid "入力されたURLが正しくありません。"
#~ msgstr "The URL entered is incorrect."

#~ msgid "非公開アカウント"
#~ msgstr "Private account"

#~ msgid "保護されたユーザ"
#~ msgstr "Protected user"

#~ msgid "Twitterアカウントの管理"
#~ msgstr "Manage Twitter accounts"

#~ msgid "規定のアカウント"
#~ msgstr "Default account"

#~ msgid "規定のアカウントに設定(&F)"
#~ msgstr "Set to de&fault account"

#~ msgid "設定中"
#~ msgstr "set"

#~ msgid "ブラウザを開いてアカウントの認証作業を行います。よろしいですか？"
#~ msgstr ""
#~ "I will open your default browser for Authorization. Do you wish to "
#~ "continue?"

#~ msgid ""
#~ "Twitterアカウントの情報が設定されていません。Twitterとの連携を停止します"
#~ "か？"
#~ msgstr ""
#~ "Your Twitter account information is not set up. Would you like to stop "
#~ "working with Twitter?"

#~ msgid "規定のアカウントが設定されていません。"
#~ msgstr "Default account is not configured."

#~ msgid "ユーザ名またはパスワードが不正です。"
#~ msgstr "User name or password is wrong."

#~ msgid "Twitterユーザ名またはパスワードが不正です。設定を確認してください。"
#~ msgstr "Twitter user name or password is wrong."

#~ msgid ""
#~ "前バージョンからの設定の引き継ぎに失敗したため、規定の設定が読み込まれまし"
#~ "た。[サービス]→[%s]→[録画形式の設定]から、設定をご確認ください。"
#~ msgstr ""
#~ "The default settings were loaded because the transfer of settings from "
#~ "the previous version failed. Check the settings from [Services]→ [%s]→ "
#~ "[Recording format settings]."

#~ msgid "ファイル形式(&T)"
#~ msgstr "File &Type"

#~ msgid ""
#~ "設定されたユーザ名またはパスワードが不正です。設定を確認してください。"
#~ msgstr "Configured ID or password is wrong."

#~ msgid "Twitterでフォローしているユーザを一括追加"
#~ msgstr "Add Users From Twitter"
